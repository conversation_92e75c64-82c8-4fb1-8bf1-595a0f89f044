import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import {
  TextField,
  Select,
  FileUpload,
} from '@/components/inputs';
import api from '@/lib/axios';
import { useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";

const UserForm = ({ userData = {}, handleModalClose = () => { } }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const initialValues = {
    name: userData.name || "",
    email: userData.email || "",
    password: "",
    role: userData.role || "",
    is_active: userData.is_active ? "1" : '0',
    profile_picture: "",
  };

  const isEditMode = Object.keys(userData).length > 0;

  const validationSchema = Yup.object().shape({
    name: Yup.string().required(t("userForm.validation.nameRequired")),
    email: Yup.string().email(t("userForm.validation.invalidEmail")).required(t("userForm.validation.emailRequired")),
    password: isEditMode
      ? Yup.string()
      : Yup.string().min(6, t("userForm.validation.passwordMin")).required(t("userForm.validation.passwordRequired")),
    role: Yup.string().required(t("userForm.validation.roleRequired")),
  });

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    formData.append('name', values.name);
    formData.append('email', values.email);
    formData.append('role', values.role);
    formData.append('is_active', values.is_active);

    if (values.password) {
      formData.append('password', values.password);
    }

    if (values.profile_picture && typeof values.profile_picture === 'object') {
      formData.append('profile_picture', values.profile_picture);
    }

    if (isEditMode) {
      formData.append('_method', 'PUT');
    }

    const url = isEditMode
      ? `admin/users/${userData.id}`
      : 'admin/users';

    try {
      await api.post(url, formData);
      queryClient.invalidateQueries('userList');
      resetForm();
      handleModalClose();
    } catch (error) {
      console.error(t("userForm.submitError"), error);
    }
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {() => (
        <Form className="grid grid-cols-2 gap-4">
          <TextField label={t("userForm.name")} name="name" />
          <TextField label={t("userForm.email")} name="email" type="email" />
          {!isEditMode && <TextField label={t("userForm.password")} name="password" type="password" />}
          <Select
            label={t("userForm.role")}
            name="role"
            options={[
              { label: t("userForm.admin"), value: "admin" },
              { label: t("userForm.author"), value: "author" },
              { label: t("userForm.publisher"), value: "publisher" },
              { label: t("userForm.user"), value: "user" },
            ]}
          />
          <Select
            label={t("userForm.isActive")}
            name="is_active"
            options={[
              { value: '1', label: t("userForm.active") },
              { value: '0', label: t("userForm.inactive") },
            ]}
          />
          <FileUpload label={t("userForm.profilePicture")} name="profile_picture" />

          <button type="submit" className="col-span-2 bg-blue-500 text-white py-2 px-4 rounded">
            {isEditMode ? t("userForm.updateUser") : t("userForm.createUser")}
          </button>
        </Form>
      )}
    </Formik>
  );
};

export default UserForm;
